import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel, validateDatabaseCode } from '@/lib/dynamicTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import type { DatabaseConfig } from '@/lib/configCache';

export const dynamic = 'force-dynamic';

// 统一搜索请求接口
interface UnifiedSearchRequest {
  advancedConditions?: SearchCondition[];
  filters?: Record<string, unknown>;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface SearchCondition {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'between' | 'in';
  value: string | string[] | number | number[] | Date | Date[];
  logic?: 'AND' | 'OR';
}

// 构建高级搜索的where条件
function buildAdvancedWhere(conditions: SearchCondition[], config: DatabaseConfig): Record<string, unknown> {
  if (!conditions || conditions.length === 0) {
    return {};
  }

  const searchableFields = config.fields.filter(f => f.isSearchable).map(f => f.fieldName);
  const filterableFields = config.fields.filter(f => f.isFilterable).map(f => f.fieldName);
  const allowedFields = [...searchableFields, ...filterableFields];

  const whereConditions: Record<string, unknown>[] = [];

  conditions.forEach(condition => {
    // 验证字段是否允许搜索/过滤
    if (!allowedFields.includes(condition.field)) {
      return;
    }

    let fieldCondition: Record<string, unknown> = {};

    switch (condition.operator) {
      case 'equals':
        fieldCondition = { [condition.field]: condition.value };
        break;
      case 'contains':
        fieldCondition = {
          [condition.field]: {
            contains: condition.value as string,
            mode: 'insensitive' as const
          }
        };
        break;
      case 'startsWith':
        fieldCondition = {
          [condition.field]: {
            startsWith: condition.value as string,
            mode: 'insensitive' as const
          }
        };
        break;
      case 'endsWith':
        fieldCondition = {
          [condition.field]: {
            endsWith: condition.value as string,
            mode: 'insensitive' as const
          }
        };
        break;
      case 'greaterThan':
        fieldCondition = {
          [condition.field]: {
            gt: condition.value
          }
        };
        break;
      case 'lessThan':
        fieldCondition = {
          [condition.field]: {
            lt: condition.value
          }
        };
        break;
      case 'between':
        if (Array.isArray(condition.value) && condition.value.length === 2) {
          fieldCondition = {
            [condition.field]: {
              gte: condition.value[0],
              lte: condition.value[1]
            }
          };
        }
        break;
      case 'in':
        fieldCondition = {
          [condition.field]: {
            in: Array.isArray(condition.value) ? condition.value : [condition.value]
          }
        };
        break;
    }

    if (Object.keys(fieldCondition).length > 0) {
      whereConditions.push(fieldCondition);
    }
  });

  if (whereConditions.length === 0) {
    return {};
  }

  // 检查是否有OR逻辑
  const hasOr = conditions.some(c => c.logic === 'OR');
  
  if (hasOr) {
    // 如果有OR逻辑，需要更复杂的处理
    const andConditions: Record<string, unknown>[] = [];
    const orConditions: Record<string, unknown>[] = [];

    whereConditions.forEach((condition, index) => {
      if (conditions[index]?.logic === 'OR') {
        orConditions.push(condition);
      } else {
        andConditions.push(condition);
      }
    });

    const result: Record<string, unknown> = {};
    if (andConditions.length > 0) {
      Object.assign(result, andConditions.length === 1 ? andConditions[0] : { AND: andConditions });
    }
    if (orConditions.length > 0) {
      if (Object.keys(result).length > 0) {
        result.OR = orConditions;
      } else {
        return orConditions.length === 1 ? orConditions[0] : { OR: orConditions };
      }
    }
    return result;
  } else {
    // 默认用AND逻辑
    return whereConditions.length === 1 ? whereConditions[0] : { AND: whereConditions };
  }
}

// 合并高级搜索条件和简单过滤器条件
function mergeSearchConditions(
  advancedWhere: Record<string, unknown>,
  filtersWhere: Record<string, unknown>
): Record<string, unknown> {
  // 如果两个都为空，返回空对象
  if (Object.keys(advancedWhere).length === 0 && Object.keys(filtersWhere).length === 0) {
    return {};
  }

  // 如果只有一个有条件，直接返回
  if (Object.keys(advancedWhere).length === 0) {
    return filtersWhere;
  }
  if (Object.keys(filtersWhere).length === 0) {
    return advancedWhere;
  }

  // 两个都有条件，用AND组合
  return {
    AND: [advancedWhere, filtersWhere]
  };
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error || 'Invalid database code' },
        { status: validation.status || 400 }
      );
    }

    const body: UnifiedSearchRequest = await request.json();
    const { 
      advancedConditions = [], 
      filters = {}, 
      page = 1, 
      limit = 20, 
      sortBy, 
      sortOrder = 'desc' 
    } = body;

    // 获取配置
    const config: DatabaseConfig = await getDatabaseConfig(database);
    const visibleFields = config.fields.filter(f => f.isVisible).map(f => f.fieldName);
    const sortableFields = config.fields.filter(f => f.isSortable).map(f => f.fieldName);

    // 构建高级搜索条件
    const advancedWhere = buildAdvancedWhere(advancedConditions, config);

    // 构建简单过滤器条件
    const searchParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, String(v)));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });



    // 临时修复：直接构建筛选条件，避免buildMedicalDeviceWhere的复杂性
    const filtersWhere: Record<string, unknown> = {};
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (value === 'N/A') {
          // N/A筛选特殊处理
          filtersWhere.OR = [
            { [key]: null },
            { [key]: '' }
          ];
        } else if (Array.isArray(value)) {
          const hasNullValue = value.includes('N/A');
          if (hasNullValue) {
            const nonNullValues = value.filter(v => v !== 'N/A');
            if (nonNullValues.length > 0) {
              filtersWhere.OR = [
                { [key]: { in: nonNullValues } },
                { [key]: null },
                { [key]: '' }
              ];
            } else {
              filtersWhere.OR = [
                { [key]: null },
                { [key]: '' }
              ];
            }
          } else {
            filtersWhere[key] = { in: value };
          }
        } else {
          filtersWhere[key] = value;
        }
      }
    });

    // 合并两种搜索条件
    const where = mergeSearchConditions(advancedWhere, filtersWhere);

    // 构建排序条件
    let orderBy: Record<string, unknown>;

    if (sortBy) {
      // 如果指定了排序字段，使用指定的排序
      orderBy = { [sortBy]: sortOrder };
    } else if (config.defaultSort && config.defaultSort.length > 0) {
      // 使用配置中的默认排序
      if (config.defaultSort.length === 1) {
        const sort = config.defaultSort[0];
        orderBy = { [sort.field]: sort.order };
      } else {
        // 多字段排序
        orderBy = config.defaultSort.map(sort => ({
          [sort.field]: sort.order
        })) as unknown as Record<string, unknown>;
      }
    } else {
      // 回退到第一个可排序字段或id
      const defaultSortField = sortableFields[0] || 'id';
      orderBy = { [defaultSortField]: sortOrder };
    }

    // 使用动态模型获取
    const model = await getDynamicModel(database);
    if (!isPrismaModel(model)) {
      return NextResponse.json(
        { success: false, error: 'Model not found or invalid' },
        { status: 500 }
      );
    }

    // 构建 select 对象
    const select: Record<string, boolean> = {};
    visibleFields.forEach(f => { select[f] = true; });
    select['id'] = true;

    const data = await model.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      select,
    }) as unknown[];

    const totalCount = await (model as any).count({ where }) as number;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      success: true,
      data,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      searchConditions: {
        advancedConditions,
        filters,
        mergedWhere: where
      },
      config,
    });

  } catch (__error) {
    console.error('Unified search error:', __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
